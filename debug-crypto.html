<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>加密功能调试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .error { color: red; }
        .success { color: green; }
        .warning { color: orange; }
        button { margin: 5px; padding: 8px 15px; }
        textarea { width: 100%; height: 100px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>加密功能调试工具</h1>
    
    <div class="test-section">
        <h2>库加载测试</h2>
        <button onclick="testLibraryLoading()">测试库加载</button>
        <div id="library-status"></div>
    </div>

    <div class="test-section">
        <h2>Base64 测试</h2>
        <textarea id="base64-input" placeholder="输入要加密的文本">Hello World</textarea>
        <button onclick="testBase64Encode()">Base64加密</button>
        <button onclick="testBase64Decode()">Base64解密</button>
        <div id="base64-result"></div>
    </div>

    <div class="test-section">
        <h2>SM4 测试</h2>
        <textarea id="sm4-input" placeholder="输入要加密的文本">Hello World</textarea>
        <button onclick="testSM4Encode()">SM4加密</button>
        <button onclick="testSM4Decode()">SM4解密</button>
        <div id="sm4-result"></div>
    </div>

    <script>
        // 复制原文件中的库加载函数
        function loadScript(src, callback) {
            const script = document.createElement('script');
            script.src = src;
            script.onload = callback || function() {};
            script.onerror = function() {
                console.error('Failed to load script:', src);
                if (callback) callback(new Error('Script load failed'));
            };
            document.head.appendChild(script);
        }

        window.loadCryptoLibs = function() {
            return new Promise((resolve, reject) => {
                if (window.cryptoLibsLoaded) {
                    resolve();
                    return;
                }

                let loadedCount = 0;
                const totalLibs = 3;
                let hasError = false;

                function checkComplete(error) {
                    if (hasError) return;

                    if (error) {
                        hasError = true;
                        reject(error);
                        return;
                    }

                    loadedCount++;
                    if (loadedCount === totalLibs) {
                        window.cryptoLibsLoaded = true;
                        resolve();
                    }
                }

                // 加载 Base64 库
                loadScript('./lib/js/js-base64-main/base64.js', checkComplete);

                // 加载 CryptoJS 库
                loadScript('https://cdnjs.cloudflare.com/ajax/libs/crypto-js/4.1.1/crypto-js.min.js', checkComplete);

                // 加载 SM4 库
                loadScript('./lib/js/sm-crypto/dist/sm4.js', checkComplete);
            });
        };

        // 修复后的 strToHex 函数
        function strToHex(key) {
            let hexKey = '';
            for (let i = 0; i < key.length; i++) {
                let code = key.charCodeAt(i);
                if (code < 16) hexKey += '0';  // 修复：使用 hexKey 而不是 hex
                hexKey += code.toString(16).toUpperCase();
            }
            return hexKey;
        }

        function hexStringToString(hexStr) {
            hexStr = hexStr.replace(/\s+/g, '');
            if (hexStr.length % 2 !== 0) {
                throw new Error('Invalid hex string');
            }
            let str = '';
            for (let i = 0; i < hexStr.length; i += 2) {
                const byte = parseInt(hexStr.substr(i, 2), 16);
                if (!isNaN(byte)) {
                    str += String.fromCharCode(byte);
                }
            }
            return str;
        }

        function base64ToHex(base64) {
            const binaryData = atob(base64);
            const len = binaryData.length;
            let bytes = new Uint8Array(len);
            for (let i = 0; i < len; i++) {
                bytes[i] = binaryData.charCodeAt(i);
            }
            let hexString = Array.from(bytes).map(function(byte) {
                return ('0' + (byte & 0xFF).toString(16)).slice(-2).toUpperCase();
            }).join('');
            return hexString;
        }

        async function testLibraryLoading() {
            const statusDiv = document.getElementById('library-status');
            statusDiv.innerHTML = '正在加载库...';
            
            try {
                await window.loadCryptoLibs();
                statusDiv.innerHTML = `
                    <div class="success">✓ 库加载成功</div>
                    <div>Base64: ${typeof Base64 !== 'undefined' ? '✓' : '✗'}</div>
                    <div>CryptoJS: ${typeof CryptoJS !== 'undefined' ? '✓' : '✗'}</div>
                    <div>SM4: ${typeof sm4 !== 'undefined' ? '✓' : '✗'}</div>
                `;
            } catch (error) {
                statusDiv.innerHTML = `<div class="error">✗ 库加载失败: ${error.message}</div>`;
                console.error('库加载失败:', error);
            }
        }

        async function testBase64Encode() {
            const input = document.getElementById('base64-input').value;
            const resultDiv = document.getElementById('base64-result');
            
            if (!input) {
                resultDiv.innerHTML = '<div class="warning">请输入文本</div>';
                return;
            }

            try {
                await window.loadCryptoLibs();
                if (typeof Base64 === 'undefined') {
                    resultDiv.innerHTML = '<div class="error">Base64库未加载</div>';
                    return;
                }
                
                const encoded = Base64.encode(input);
                resultDiv.innerHTML = `<div class="success">加密结果: ${encoded}</div>`;
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">加密失败: ${error.message}</div>`;
                console.error('Base64加密失败:', error);
            }
        }

        async function testBase64Decode() {
            const input = document.getElementById('base64-input').value;
            const resultDiv = document.getElementById('base64-result');
            
            if (!input) {
                resultDiv.innerHTML = '<div class="warning">请输入文本</div>';
                return;
            }

            try {
                await window.loadCryptoLibs();
                if (typeof Base64 === 'undefined') {
                    resultDiv.innerHTML = '<div class="error">Base64库未加载</div>';
                    return;
                }
                
                const decoded = Base64.decode(input);
                resultDiv.innerHTML = `<div class="success">解密结果: ${decoded}</div>`;
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">解密失败: ${error.message}</div>`;
                console.error('Base64解密失败:', error);
            }
        }

        async function testSM4Encode() {
            const input = document.getElementById('sm4-input').value;
            const resultDiv = document.getElementById('sm4-result');
            
            if (!input) {
                resultDiv.innerHTML = '<div class="warning">请输入文本</div>';
                return;
            }

            try {
                await window.loadCryptoLibs();
                if (typeof sm4 === 'undefined') {
                    resultDiv.innerHTML = '<div class="error">SM4库未加载</div>';
                    return;
                }
                
                const key = "1234567812345678";
                const hexk = strToHex(key);
                console.log('Key:', key, 'HexKey:', hexk);
                
                const re = sm4.encrypt(input, hexk);
                const result = window.btoa(hexStringToString(re));
                resultDiv.innerHTML = `<div class="success">加密结果: ${result}</div>`;
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">加密失败: ${error.message}</div>`;
                console.error('SM4加密失败:', error);
            }
        }

        async function testSM4Decode() {
            const input = document.getElementById('sm4-input').value;
            const resultDiv = document.getElementById('sm4-result');
            
            if (!input) {
                resultDiv.innerHTML = '<div class="warning">请输入文本</div>';
                return;
            }

            try {
                await window.loadCryptoLibs();
                if (typeof sm4 === 'undefined') {
                    resultDiv.innerHTML = '<div class="error">SM4库未加载</div>';
                    return;
                }
                
                const key = "1234567812345678";
                const hexk = strToHex(key);
                
                let hexinput = base64ToHex(input);
                let ree = sm4.decrypt(hexinput, hexk);
                resultDiv.innerHTML = `<div class="success">解密结果: ${ree}</div>`;
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">解密失败: ${error.message}</div>`;
                console.error('SM4解密失败:', error);
            }
        }

        // 页面加载时自动测试库加载
        window.onload = function() {
            testLibraryLoading();
        };
    </script>
</body>
</html>
